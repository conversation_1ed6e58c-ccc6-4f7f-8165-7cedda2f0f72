import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import cv2
import pytesseract
import numpy as np
from PIL import Image, ImageTk
import re
import os

class WordCountCalculatorUI:
    def __init__(self, root):
        self.root = root
        self.root.title("字數計算器")
        self.root.geometry("800x600")
        
        self.image_path = None
        self.numbers = []
        self.selected_numbers = []
        
        self.setup_ui()
    
    def setup_ui(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 頂部：文件選擇區域
        file_frame = ttk.LabelFrame(main_frame, text="圖片選擇", padding="5")
        file_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.file_path_var = tk.StringVar()
        ttk.Entry(file_frame, textvariable=self.file_path_var, width=50).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(file_frame, text="選擇圖片", command=self.select_image).grid(row=0, column=1, padx=(5, 0))
        
        # 左側：圖片顯示區域
        left_frame = ttk.LabelFrame(main_frame, text="圖片預覽", padding="5")
        left_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 圖片顯示畫布 - 調整預覽框大小
        self.image_canvas = tk.Canvas(left_frame, width=260, height=370, bg='white')
        self.image_canvas.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 圖片滾動條
        img_v_scrollbar = ttk.Scrollbar(left_frame, orient='vertical', command=self.image_canvas.yview)
        img_v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.image_canvas.configure(yscrollcommand=img_v_scrollbar.set)
        
        img_h_scrollbar = ttk.Scrollbar(left_frame, orient='horizontal', command=self.image_canvas.xview)
        img_h_scrollbar.grid(row=1, column=0, sticky=(tk.E, tk.W))
        self.image_canvas.configure(xscrollcommand=img_h_scrollbar.set)
        
        # 右側：操作和結果區域
        right_frame = ttk.Frame(main_frame)
        right_frame.grid(row=1, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 操作按鈕區域
        button_frame = ttk.LabelFrame(right_frame, text="操作", padding="5")
        button_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Button(button_frame, text="識別並計算", command=self.auto_calculate, width=15).grid(row=0, column=0, padx=(0, 5))
        ttk.Button(button_frame, text="手動識別", command=self.extract_numbers, width=15).grid(row=0, column=1, padx=(5, 0))
        
        # 識別結果區域
        numbers_frame = ttk.LabelFrame(right_frame, text="識別到的數字", padding="5")
        numbers_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 10))
        
        self.numbers_listbox = tk.Listbox(numbers_frame, height=6, selectmode=tk.MULTIPLE)
        self.numbers_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        numbers_scrollbar = ttk.Scrollbar(numbers_frame, orient=tk.VERTICAL, command=self.numbers_listbox.yview)
        numbers_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.numbers_listbox.configure(yscrollcommand=numbers_scrollbar.set)
        
        ttk.Button(numbers_frame, text="手動計算選中數字", command=self.calculate_result).grid(row=1, column=0, columnspan=2, pady=(5, 0))
        
        # 計算結果區域
        result_frame = ttk.LabelFrame(right_frame, text="計算結果", padding="5")
        result_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        self.result_text = tk.Text(result_frame, height=15, width=45, wrap=tk.WORD)
        self.result_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        result_scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=self.result_text.yview)
        result_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.result_text.configure(yscrollcommand=result_scrollbar.set)
        
        # 配置網格權重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(1, weight=1)
        left_frame.columnconfigure(0, weight=1)
        left_frame.rowconfigure(0, weight=1)
        right_frame.columnconfigure(0, weight=1)
        right_frame.rowconfigure(2, weight=1)
        numbers_frame.columnconfigure(0, weight=1)
        result_frame.columnconfigure(0, weight=1)
        result_frame.rowconfigure(0, weight=1)
    
    def select_image(self):
        file_path = filedialog.askopenfilename(
            title="選擇圖片",
            filetypes=[
                ("圖片文件", "*.png *.jpg *.jpeg *.bmp *.tiff"),
                ("所有文件", "*.*")
            ]
        )
        if file_path:
            self.image_path = file_path
            self.file_path_var.set(file_path)
            self.display_image()
    
    def display_image(self):
        """顯示選中的圖片"""
        if not self.image_path or not os.path.exists(self.image_path):
            return
        
        try:
            # 讀取並顯示圖片
            image = Image.open(self.image_path)
            
            # 計算適合的顯示尺寸（保持比例）
            canvas_width = 300
            canvas_height = 400
            img_width, img_height = image.size
            
            # 計算縮放比例
            scale_w = canvas_width / img_width
            scale_h = canvas_height / img_height
            scale = min(scale_w, scale_h, 1.0)  # 不放大，只縮小
            
            new_width = int(img_width * scale)
            new_height = int(img_height * scale)
            
            # 調整圖片大小
            resized_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            self.photo = ImageTk.PhotoImage(resized_image)
            
            # 清空畫布並顯示圖片
            self.image_canvas.delete("all")
            self.image_canvas.create_image(new_width//2, new_height//2, image=self.photo)
            self.image_canvas.configure(scrollregion=(0, 0, new_width, new_height))
            
        except Exception as e:
            messagebox.showerror("錯誤", f"無法顯示圖片: {str(e)}")
    
    def extract_numbers(self):
        if not self.image_path:
            messagebox.showerror("錯誤", "請先選擇圖片")
            return
        
        if not os.path.exists(self.image_path):
            messagebox.showerror("錯誤", "圖片文件不存在")
            return
        
        try:
            # 清空之前的結果
            self.numbers_listbox.delete(0, tk.END)
            self.result_text.delete(1.0, tk.END)
            
            # 讀取圖片
            image = cv2.imread(self.image_path)
            if image is None:
                raise ValueError("無法讀取圖片")
            
            # 轉換為灰度圖
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            # 先嘗試全圖識別
            self.result_text.insert(tk.END, "=== 全圖識別 ===\n")
            
            # 增強對比度
            enhanced = cv2.convertScaleAbs(gray, alpha=1.5, beta=0)
            
            # 使用OCR識別文字
            custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789,'
            full_text = pytesseract.image_to_string(enhanced, config=custom_config)
            
            self.result_text.insert(tk.END, f"全圖OCR結果:\n{full_text}\n")
            self.result_text.insert(tk.END, "-" * 40 + "\n")
            
            # 從全圖結果提取數字
            self.numbers = []
            lines = full_text.split('\n')
            
            for line in lines:
                # 尋找包含逗號的數字格式（如 53,960）
                comma_numbers = re.findall(r'\d{1,3}(?:,\d{3})+', line)
                for num_str in comma_numbers:
                    num = int(num_str.replace(',', ''))
                    self.numbers.append(num)
                    self.numbers_listbox.insert(tk.END, f"{num:,} (全圖識別)")
                
                # 尋找普通數字
                plain_numbers = re.findall(r'\b\d+\b', line)
                for num_str in plain_numbers:
                    # 避免重複添加已經處理過的逗號數字
                    if ',' not in line or not any(num_str in comma_num for comma_num in comma_numbers):
                        num = int(num_str)
                        if num > 100:  # 只考慮較大的數字
                            self.numbers.append(num)
                            self.numbers_listbox.insert(tk.END, f"{num:,} (全圖識別)")
            
            # 如果全圖識別效果不好，嘗試區域識別
            if len(self.numbers) < 2:
                self.result_text.insert(tk.END, "\n=== 區域識別 ===\n")
                
                # 定義數字區域的座標 (x, y, width, height)
                # 這些座標需要根據你的圖片調整
                regions = [
                    {"name": "左上區域", "coords": (50, 50, 200, 50)},
                    {"name": "中上區域", "coords": (50, 100, 200, 50)},
                    {"name": "左中區域", "coords": (50, 150, 200, 50)},
                    {"name": "右上區域", "coords": (250, 50, 200, 50)},
                    {"name": "右中區域", "coords": (250, 100, 200, 50)},
                    {"name": "右下區域", "coords": (250, 150, 200, 50)},
                ]
                
                for region in regions:
                    x, y, w, h = region["coords"]
                    
                    # 確保座標不超出圖片範圍
                    if x + w > gray.shape[1] or y + h > gray.shape[0]:
                        continue
                    
                    # 裁切指定區域
                    roi = gray[y:y+h, x:x+w]
                    
                    # 增強對比度
                    enhanced_roi = cv2.convertScaleAbs(roi, alpha=2.0, beta=0)
                    
                    # 二值化處理
                    _, binary = cv2.threshold(enhanced_roi, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
                    
                    # OCR識別
                    custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789,'
                    text = pytesseract.image_to_string(binary, config=custom_config)
                    
                    # 提取數字
                    numbers_in_region = re.findall(r'\d{1,3}(?:,\d{3})+|\d+', text.strip())
                    
                    if text.strip():
                        self.result_text.insert(tk.END, f"{region['name']}: {text.strip()}\n")
                    
                    for num_str in numbers_in_region:
                        if num_str:
                            num = int(num_str.replace(',', ''))
                            if num > 100:  # 過濾小數字
                                self.numbers.append(num)
                                self.numbers_listbox.insert(tk.END, f"{num:,} ({region['name']})")
            
            self.result_text.insert(tk.END, f"\n找到 {len(self.numbers)} 個數字\n")
            
            if len(self.numbers) >= 2:
                self.result_text.insert(tk.END, "請手動選擇兩個數字進行計算\n")
            else:
                self.result_text.insert(tk.END, "識別到的數字太少，請檢查圖片或調整識別參數\n")
                self.result_text.insert(tk.END, "建議:\n")
                self.result_text.insert(tk.END, "1. 確保圖片清晰\n")
                self.result_text.insert(tk.END, "2. 數字區域對比度足夠\n")
                self.result_text.insert(tk.END, "3. 調整區域座標\n")
        
        except Exception as e:
            messagebox.showerror("錯誤", f"處理圖片時發生錯誤:\n{str(e)}")

    def calculate_result(self):
        selected_indices = self.numbers_listbox.curselection()
        
        if len(selected_indices) != 2:
            messagebox.showwarning("警告", "請選擇兩個數字進行計算")
            return
        
        # 獲取選中的數字
        num1 = self.numbers[selected_indices[0]]
        num2 = self.numbers[selected_indices[1]]
        
        # 確定大數和小數
        large_num = max(num1, num2)
        small_num = min(num1, num2)
        
        # 計算公式: (大數 - 小數) / 3 + 小數
        result = (large_num - small_num) / 3 + small_num
        
        # 顯示計算過程
        self.result_text.insert(tk.END, "\n" + "="*50 + "\n")
        self.result_text.insert(tk.END, "計算過程:\n")
        self.result_text.insert(tk.END, f"選擇的數字: {large_num:,} 和 {small_num:,}\n")
        self.result_text.insert(tk.END, f"計算公式: ({large_num:,} - {small_num:,}) / 3 + {small_num:,}\n")
        self.result_text.insert(tk.END, f"= ({large_num - small_num:,}) / 3 + {small_num:,}\n")
        self.result_text.insert(tk.END, f"= {(large_num - small_num) / 3:.10f} + {small_num:,}\n")
        self.result_text.insert(tk.END, f"= {result:.10f}\n")
        self.result_text.insert(tk.END, f"四捨五入到6位小數: {result:.6f}\n")
        self.result_text.insert(tk.END, "="*50 + "\n")
        
        # 滾動到底部
        self.result_text.see(tk.END)

    def auto_calculate(self):
        """使用固定座標範圍自動抓取數字"""
        if not self.image_path:
            messagebox.showwarning("警告", "請先選擇圖片")
            return
        
        try:
            # 讀取圖片
            image = cv2.imread(self.image_path)
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            
            self.result_text.insert(tk.END, "\n=== 座標區域識別 ===\n")
            
            # 定義固定座標區域 - 只保留目標數字位置
            regions = [
                {"name": "9831區域", "coords": (0, 90, 256, 50)},        # 中部區域，找到 9,831
                {"name": "53960區域", "coords": (0, 120, 256, 50)},      # 中下區域，找到 53,960
            ]
            
            found_numbers = []
            
            for region in regions:
                x, y, w, h = region["coords"]
                
                # 確保座標不超出圖片範圍
                if x + w > gray.shape[1] or y + h > gray.shape[0]:
                    h = min(h, gray.shape[0] - y)  # 調整高度
                    if h <= 0:
                        self.result_text.insert(tk.END, f"{region['name']}: 座標超出範圍\n")
                        continue
                
                # 裁切指定區域
                roi = gray[y:y+h, x:x+w]
                
                # 多種增強方式
                enhanced1 = cv2.convertScaleAbs(roi, alpha=1.5, beta=0)
                enhanced2 = cv2.convertScaleAbs(roi, alpha=2.0, beta=0)
                
                # 嘗試不同的OCR配置
                configs = [
                    r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789,',
                    r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789,',
                    r'--oem 3 --psm 7 -c tessedit_char_whitelist=0123456789,',
                ]
                
                all_text = []
                
                for enhanced in [enhanced1, enhanced2]:
                    for config in configs:
                        try:
                            text = pytesseract.image_to_string(enhanced, config=config)
                            if text.strip():
                                all_text.append(text.strip())
                        except:
                            continue
                
                # 合併所有識別結果
                combined_text = ' '.join(all_text)
                
                # 提取數字，降低過濾門檻
                numbers_in_region = re.findall(r'\d{1,3}(?:,\d{3})+|\d+', combined_text)
                
                self.result_text.insert(tk.END, f"{region['name']} ({x},{y},{w},{h}): ")
                
                if combined_text:
                    self.result_text.insert(tk.END, f"識別文字='{combined_text[:50]}...' " if len(combined_text) > 50 else f"識別文字='{combined_text}' ")
                
                region_numbers = []
                for num_str in numbers_in_region:
                    if num_str:
                        num = int(num_str.replace(',', ''))
                        if num > 10:  # 降低過濾門檻，只過濾個位數
                            region_numbers.append(num)
                            found_numbers.append(num)
                
                if region_numbers:
                    self.result_text.insert(tk.END, f"找到數字: {[f'{n:,}' for n in region_numbers]}")
                else:
                    self.result_text.insert(tk.END, "未找到數字")
                
                self.result_text.insert(tk.END, "\n")
            
            # 顯示圖片尺寸信息
            self.result_text.insert(tk.END, f"\n圖片尺寸: {gray.shape[1]} x {gray.shape[0]} (寬x高)\n")
            
            if len(found_numbers) >= 2:
                # 去除重複數字
                unique_numbers = []
                seen = set()
                for num in found_numbers:
                    if num not in seen:
                        unique_numbers.append(num)
                        seen.add(num)
                
                self.result_text.insert(tk.END, f"去重後的數字: {[f'{n:,}' for n in unique_numbers]}\n")
                
                # 優先尋找目標數字組合
                target_9831 = None
                target_53960 = None
                
                for num in unique_numbers:
                    if 9800 <= num <= 9900:  # 尋找接近 9,831 的數字
                        target_9831 = num
                    elif 53000 <= num <= 54000:  # 尋找接近 53,960 的數字
                        target_53960 = num
                
                if target_9831 and target_53960:
                    self.result_text.insert(tk.END, f"找到目標數字組合: {target_9831:,} 和 {target_53960:,}\n")
                    self._perform_calculation(target_9831, target_53960, "目標數字匹配")
                elif len(unique_numbers) >= 2:
                    # 如果沒找到目標組合，過濾掉太小的數字，選擇較大的兩個
                    large_numbers = [n for n in unique_numbers if n > 1000]
                    if len(large_numbers) >= 2:
                        sorted_numbers = sorted(large_numbers, reverse=True)
                        num1, num2 = sorted_numbers[0], sorted_numbers[1]
                        self.result_text.insert(tk.END, f"使用較大的兩個數字: {num1:,} 和 {num2:,}\n")
                        self._perform_calculation(num1, num2, "座標區域識別")
                    else:
                        # 使用前兩個不重複的數字
                        num1, num2 = unique_numbers[0], unique_numbers[1]
                        self.result_text.insert(tk.END, f"使用前兩個數字: {num1:,} 和 {num2:,}\n")
                        self._perform_calculation(num1, num2, "座標區域識別")
                else:
                    messagebox.showwarning("警告", "去重後數字不足")
            else:
                self.result_text.insert(tk.END, f"只找到 {len(found_numbers)} 個數字，需要調整座標範圍\n")
                self.result_text.insert(tk.END, "請根據上面的信息調整座標，或使用手動選擇\n")
                messagebox.showwarning("警告", "無法找到足夠的數字，請調整座標範圍")
            
        except Exception as e:
            messagebox.showerror("錯誤", f"座標識別時發生錯誤:\n{str(e)}")
    
    def _find_number_near_position(self, gray, pos, direction="right"):
        """在指定位置附近尋找數字"""
        x, y, w, h = pos
        
        if direction == "right":
            # 在右側尋找數字
            search_x = x + w + 10
            search_y = y
            search_w = 100
            search_h = h + 10
        
        # 確保搜尋區域在圖片範圍內
        if search_x + search_w > gray.shape[1] or search_y + search_h > gray.shape[0]:
            return None
        
        roi = gray[search_y:search_y+search_h, search_x:search_x+search_w]
        enhanced = cv2.convertScaleAbs(roi, alpha=2.0, beta=0)
        
        custom_config = r'--oem 3 --psm 8 -c tessedit_char_whitelist=0123456789,'
        text = pytesseract.image_to_string(enhanced, config=custom_config)
        
        numbers = re.findall(r'\d{1,3}(?:,\d{3})+|\d+', text.strip())
        for num_str in numbers:
            if num_str:
                num = int(num_str.replace(',', ''))
                if num > 100:  # 過濾小數字
                    return num
        
        return None
    
    def _perform_calculation(self, num1, num2, method):
        """執行計算並顯示結果"""
        large_num = max(num1, num2)
        small_num = min(num1, num2)
        
        result = (large_num - small_num) / 3 + small_num
        
        self.result_text.insert(tk.END, "\n" + "="*50 + "\n")
        self.result_text.insert(tk.END, f"自動計算結果 ({method}):\n")
        self.result_text.insert(tk.END, f"找到的數字: {large_num:,} 和 {small_num:,}\n")
        self.result_text.insert(tk.END, f"計算公式: ({large_num:,} - {small_num:,}) / 3 + {small_num:,}\n")
        self.result_text.insert(tk.END, f"= ({large_num - small_num:,}) / 3 + {small_num:,}\n")
        self.result_text.insert(tk.END, f"= {(large_num - small_num) / 3:.10f} + {small_num:,}\n")
        self.result_text.insert(tk.END, f"= {result:.10f}\n")
        self.result_text.insert(tk.END, f"四捨五入到6位小數: {result:.6f}\n")
        self.result_text.insert(tk.END, "="*50 + "\n")
        
        self.result_text.see(tk.END)

def main():
    root = tk.Tk()
    app = WordCountCalculatorUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()










