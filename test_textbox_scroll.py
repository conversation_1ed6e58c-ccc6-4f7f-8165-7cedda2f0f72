#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文字框滾動控制功能測試腳本
測試當滾動關閉時文字選取功能是否被正確禁用
"""

import tkinter as tk
from tkinter import ttk, messagebox

class TextBoxScrollTest:
    def __init__(self, root):
        self.root = root
        self.root.title("文字框滾動控制測試")
        self.root.geometry("800x600")
        
        # 控制變數
        self.textbox_scroll_enabled = True
        
        self.setup_ui()
        
    def setup_ui(self):
        # 控制面板
        control_frame = ttk.Frame(self.root)
        control_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        # 滾動控制按鈕
        self.scroll_button = ttk.Button(
            control_frame, 
            text="文字框滾動: 開", 
            command=self.toggle_scroll
        )
        self.scroll_button.pack(side=tk.LEFT, padx=5)
        
        # 狀態標籤
        self.status_label = ttk.Label(
            control_frame, 
            text="狀態：滾動功能已開啟，文字可以選取"
        )
        self.status_label.pack(side=tk.LEFT, padx=20)
        
        # 測試說明
        info_frame = ttk.LabelFrame(self.root, text="測試說明")
        info_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
        
        info_text = """
        測試步驟：
        1. 點擊「文字框滾動: 開/關」按鈕切換功能
        2. 當滾動關閉時，嘗試以下操作：
           - 用滑鼠選取文字
           - 按 Ctrl+A 全選
           - 按 Ctrl+C 複製
           - 按 Ctrl+X 剪下
           - 右鍵點擊查看選單
        3. 觀察這些操作是否被正確禁用
        """
        
        info_label = ttk.Label(info_frame, text=info_text, justify=tk.LEFT)
        info_label.pack(padx=10, pady=5)
        
        # 文字框
        text_frame = ttk.LabelFrame(self.root, text="測試文字框")
        text_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # 創建文字框和滾動條
        self.text_widget = tk.Text(
            text_frame, 
            wrap=tk.WORD, 
            font=('標楷體', 12),
            height=15
        )
        
        scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=self.text_widget.yview)
        self.text_widget.configure(yscrollcommand=scrollbar.set)
        
        self.text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 插入測試文字
        test_text = """這是一個測試文字框，用來驗證滾動關閉時的文字選取限制功能。

當滾動功能開啟時，您可以正常選取和複製文字。
當滾動功能關閉時，文字選取功能會被禁用，無法選取文字。

您可以嘗試：
1. 開啟滾動功能，然後選取文字
2. 關閉滾動功能，再嘗試選取文字
3. 觀察兩種狀態下的差異

這個功能適用於需要防止用戶複製特定內容的場景，例如：
- 版權保護的內容
- 機密文件預覽
- 付費內容的預覽模式

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.

這是一個很長的段落，用來測試滾動功能。當文字內容超過文字框的可視區域時，會出現滾動條。當滾動功能被關閉時，不僅滾動條會被禁用，文字選取功能也會被禁用，這樣可以防止用戶複製受保護的內容。

測試各種選取方式：
- 滑鼠拖拽選取
- 雙擊選取單詞
- 三擊選取段落
- Ctrl+A 全選
- Ctrl+C 複製
- Ctrl+X 剪下
- 右鍵選單

當滾動功能關閉時，這些操作都應該被禁用。
"""
        
        self.text_widget.insert("1.0", test_text)
        
        # 綁定事件
        self.text_widget.bind('<Button-1>', self.on_text_click)
        self.text_widget.bind('<Button-3>', self.on_text_right_click)
        self.text_widget.bind('<Control-a>', self.on_text_select_all)
        self.text_widget.bind('<Control-c>', self.on_text_copy)
        self.text_widget.bind('<Control-x>', self.on_text_cut)
        self.text_widget.bind('<MouseWheel>', self.on_text_mousewheel)
        
    def toggle_scroll(self):
        """切換文字框滾動功能"""
        self.textbox_scroll_enabled = not self.textbox_scroll_enabled
        
        if self.textbox_scroll_enabled:
            self.scroll_button.config(text="文字框滾動: 開")
            self.status_label.config(text="狀態：滾動功能已開啟，文字可以選取")
        else:
            self.scroll_button.config(text="文字框滾動: 關")
            self.status_label.config(text="狀態：滾動功能已關閉，文字無法選取")
            # 清除選取狀態
            self.text_widget.tag_remove("sel", "1.0", "end")
    
    def on_text_click(self, event):
        """處理文字框點擊事件"""
        if not self.textbox_scroll_enabled:
            return "break"  # 禁用文字選取
        return None
    
    def on_text_right_click(self, event):
        """處理文字框右鍵事件"""
        if not self.textbox_scroll_enabled:
            return "break"  # 禁用右鍵選單
        return None
    
    def on_text_select_all(self, event):
        """處理Ctrl+A全選事件"""
        if not self.textbox_scroll_enabled:
            return "break"  # 禁用全選
        return None
    
    def on_text_copy(self, event):
        """處理Ctrl+C複製事件"""
        if not self.textbox_scroll_enabled:
            return "break"  # 禁用複製
        return None
    
    def on_text_cut(self, event):
        """處理Ctrl+X剪下事件"""
        if not self.textbox_scroll_enabled:
            return "break"  # 禁用剪下
        return None
    
    def on_text_mousewheel(self, event):
        """處理滾輪事件"""
        if not self.textbox_scroll_enabled:
            return "break"  # 禁用滾動
        return None

def main():
    root = tk.Tk()
    app = TextBoxScrollTest(root)
    root.mainloop()

if __name__ == "__main__":
    main() 