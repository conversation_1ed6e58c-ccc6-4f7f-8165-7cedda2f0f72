#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試滾動修復功能
"""

import tkinter as tk
from tkinter import ttk

def test_scroll_fix():
    """測試滾動修復功能"""
    root = tk.Tk()
    root.title("滾動修復測試")
    root.geometry("800x600")
    
    # 控制變數
    textbox_scroll_enabled = True
    
    def toggle_scroll():
        nonlocal textbox_scroll_enabled
        textbox_scroll_enabled = not textbox_scroll_enabled
        
        if textbox_scroll_enabled:
            toggle_btn.config(text="文字框滾動: 開")
            status_label.config(text="狀態：滾動功能已開啟，文字可以選取")
            scrollbar.configure(command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
        else:
            toggle_btn.config(text="文字框滾動: 關")
            status_label.config(text="狀態：滾動功能已關閉，文字無法選取")
            # 清除選取狀態並禁用滾動條
            text_widget.tag_remove("sel", "1.0", "end")
            scrollbar.configure(command=None)
            # 禁用文字框的滾動功能
            text_widget.configure(yscrollcommand=None)
            # 強制滾動到頂部
            text_widget.yview_moveto(0)
            # 禁用文字框的拖拽滾動功能
            text_widget.configure(state='disabled')
            # 立即重新啟用以保持可讀性，但禁用滾動
            text_widget.configure(state='normal')
    
    def on_text_click(event):
        """處理文字框點擊事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用文字選取
        return None
    
    def on_text_drag(event):
        """處理拖拽事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用拖拽選取
        return None
    
    def on_text_release(event):
        """處理釋放事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用選取
        return None
    
    def on_text_mousewheel(event):
        """處理滾輪事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用滾動
        return None
    
    # 創建UI
    control_frame = ttk.Frame(root)
    control_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
    
    toggle_btn = ttk.Button(control_frame, text="文字框滾動: 開", command=toggle_scroll)
    toggle_btn.pack(side=tk.LEFT, padx=5)
    
    status_label = ttk.Label(control_frame, text="狀態：滾動功能已開啟，文字可以選取")
    status_label.pack(side=tk.LEFT, padx=20)
    
    # 文字框
    text_frame = ttk.LabelFrame(root, text="測試文字框")
    text_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=5)
    
    text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('標楷體', 12), height=15)
    scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 插入長文字來測試滾動
    long_text = """這是一個很長的文字，用來測試滾動功能。

當滾動功能開啟時，您可以正常選取和複製文字，也可以滾動查看內容。
當滾動功能關閉時，文字選取功能會被禁用，無法選取文字，也無法滾動。

測試步驟：
1. 點擊「文字框滾動: 開/關」按鈕切換功能
2. 當滾動關閉時，嘗試以下操作：
   - 用滑鼠點擊選取文字
   - 按住滑鼠左鍵拖拽選取文字
   - 按住滑鼠左鍵拖拽滾動文字內容
   - 滾輪滾動
3. 觀察這些操作是否被正確禁用

這個功能適用於需要防止用戶複製特定內容的場景。

""" * 20  # 重複20次來製造長文字
    
    text_widget.insert("1.0", long_text)
    
    # 綁定事件
    text_widget.bind('<Button-1>', on_text_click)
    text_widget.bind('<B1-Motion>', on_text_drag)
    text_widget.bind('<ButtonRelease-1>', on_text_release)
    text_widget.bind('<MouseWheel>', on_text_mousewheel)
    
    # 說明標籤
    info_label = ttk.Label(root, text="重點測試：當滾動關閉時，按住滑鼠左鍵拖拽應該無法滾動文字內容")
    info_label.pack(side=tk.BOTTOM, pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    test_scroll_fix() 