import cv2
import pytesseract
import numpy as np
from PIL import Image
import re

def extract_numbers_from_image(image_path):
    """從圖片中提取數字"""
    # 讀取圖片
    image = cv2.imread(image_path)
    if image is None:
        raise ValueError(f"無法讀取圖片: {image_path}")
    
    # 轉換為灰度圖
    gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
    
    # 增強對比度
    enhanced = cv2.convertScaleAbs(gray, alpha=1.5, beta=0)
    
    # 使用OCR識別文字
    # 設定OCR參數，只識別數字和逗號
    custom_config = r'--oem 3 --psm 6 -c tessedit_char_whitelist=0123456789,'
    text = pytesseract.image_to_string(enhanced, config=custom_config, lang='chi_tra+eng')
    
    print("OCR識別結果:")
    print(text)
    print("-" * 40)
    
    # 提取數字（包含逗號分隔的數字）
    numbers = []
    lines = text.split('\n')
    
    for line in lines:
        # 尋找包含逗號的數字格式（如 53,960）
        comma_numbers = re.findall(r'\d{1,3}(?:,\d{3})+', line)
        for num_str in comma_numbers:
            # 移除逗號並轉換為整數
            num = int(num_str.replace(',', ''))
            numbers.append(num)
        
        # 尋找普通數字
        plain_numbers = re.findall(r'\b\d+\b', line)
        for num_str in plain_numbers:
            # 避免重複添加已經處理過的逗號數字
            if ',' not in line or not any(num_str in comma_num for comma_num in comma_numbers):
                num = int(num_str)
                if num > 1000:  # 只考慮較大的數字
                    numbers.append(num)
    
    return numbers

def calculate_word_count(numbers):
    """根據提取的數字計算字數"""
    if len(numbers) < 2:
        raise ValueError("需要至少兩個數字進行計算")
    
    # 尋找目標數字 53960 和 9831
    target_large = None
    target_small = None
    
    for num in numbers:
        if 53000 <= num <= 54000:  # 尋找接近53960的數字
            target_large = num
        elif 9000 <= num <= 10000:  # 尋找接近9831的數字
            target_small = num
    
    if target_large is None or target_small is None:
        print(f"找到的數字: {numbers}")
        print("無法找到目標數字，使用最大和次大的數字進行計算")
        sorted_numbers = sorted(numbers, reverse=True)
        if len(sorted_numbers) >= 2:
            target_large = sorted_numbers[0]
            target_small = sorted_numbers[1]
        else:
            raise ValueError("數字不足以進行計算")
    
    # 計算公式: (大數 - 小數) / 3 + 小數
    result = (target_large - target_small) / 3 + target_small
    
    print(f"使用數字: {target_large} 和 {target_small}")
    print(f"計算公式: ({target_large} - {target_small}) / 3 + {target_small}")
    print(f"計算結果: {result}")
    
    return result

def main():
    # 使用方法
    image_path = "word_stats.png"  # 請替換為你的圖片路徑
    
    try:
        # 提取數字
        numbers = extract_numbers_from_image(image_path)
        print(f"提取到的數字: {numbers}")
        
        # 計算結果
        result = calculate_word_count(numbers)
        print(f"\n最終結果: {result:.6f}")
        
    except Exception as e:
        print(f"錯誤: {e}")
        print("\n請確保:")
        print("1. 已安裝 pytesseract: pip install pytesseract")
        print("2. 已安裝 opencv-python: pip install opencv-python")
        print("3. 已安裝 Tesseract OCR 軟體")
        print("4. 圖片路徑正確")

if __name__ == "__main__":
    main()