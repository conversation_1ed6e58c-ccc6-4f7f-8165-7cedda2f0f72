#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程式文字選取控制功能測試
"""

import tkinter as tk
from tkinter import ttk, messagebox

def test_textbox_scroll_control():
    """測試文字框滾動控制功能"""
    root = tk.Tk()
    root.title("主程式文字選取控制測試")
    root.geometry("600x400")
    
    # 模擬主程式的變數
    textbox_scroll_enabled = True
    
    def toggle_scroll():
        nonlocal textbox_scroll_enabled
        textbox_scroll_enabled = not textbox_scroll_enabled
        
        if textbox_scroll_enabled:
            toggle_btn.config(text="文字框滾動: 開")
            status_label.config(text="狀態：滾動功能已開啟，文字可以選取")
            scrollbar.configure(command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)
        else:
            toggle_btn.config(text="文字框滾動: 關")
            status_label.config(text="狀態：滾動功能已關閉，文字無法選取")
            # 清除選取狀態並禁用滾動條
            text_widget.tag_remove("sel", "1.0", "end")
            scrollbar.configure(command=None)
            # 禁用文字框的滾動功能
            text_widget.configure(yscrollcommand=None)
            # 強制滾動到頂部
            text_widget.yview_moveto(0)
            # 禁用文字框的拖拽滾動功能
            text_widget.configure(state='disabled')
            # 立即重新啟用以保持可讀性，但禁用滾動
            text_widget.configure(state='normal')
    
    def on_text_click(event):
        """處理文字框點擊事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用文字選取
        return None
    
    def on_text_right_click(event):
        """處理文字框右鍵事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用右鍵選單
        return None
    
    def on_text_select_all(event):
        """處理Ctrl+A全選事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用全選
        return None
    
    def on_text_copy(event):
        """處理Ctrl+C複製事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用複製
        return None
    
    def on_text_cut(event):
        """處理Ctrl+X剪下事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用剪下
        return None
    
    def on_text_mousewheel(event):
        """處理滾輪事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用滾動
        return None
    
    def on_text_drag(event):
        """處理拖拽事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用拖拽選取
        return None
    
    def on_text_release(event):
        """處理釋放事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用選取
        return None
    
    def on_text_double_click(event):
        """處理雙擊事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用雙擊選取
        return None
    
    def on_text_triple_click(event):
        """處理三擊事件"""
        if not textbox_scroll_enabled:
            return "break"  # 禁用三擊選取
        return None
    
    # 創建UI
    control_frame = ttk.Frame(root)
    control_frame.pack(side=tk.TOP, fill=tk.X, padx=10, pady=5)
    
    toggle_btn = ttk.Button(control_frame, text="文字框滾動: 開", command=toggle_scroll)
    toggle_btn.pack(side=tk.LEFT, padx=5)
    
    status_label = ttk.Label(control_frame, text="狀態：滾動功能已開啟，文字可以選取")
    status_label.pack(side=tk.LEFT, padx=20)
    
    # 文字框
    text_frame = ttk.LabelFrame(root, text="測試文字框")
    text_frame.pack(side=tk.TOP, fill=tk.BOTH, expand=True, padx=10, pady=5)
    
    text_widget = tk.Text(text_frame, wrap=tk.WORD, font=('標楷體', 12), height=10)
    scrollbar = ttk.Scrollbar(text_frame, orient='vertical', command=text_widget.yview)
    text_widget.configure(yscrollcommand=scrollbar.set)
    
    text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    # 插入測試文字
    test_text = """這是一個測試文字框，用來驗證主程式的文字選取控制功能。

當滾動功能開啟時，您可以正常選取和複製文字。
當滾動功能關閉時，文字選取功能會被禁用，無法選取文字。

測試步驟：
1. 點擊「文字框滾動: 開/關」按鈕切換功能
2. 當滾動關閉時，嘗試以下操作：
   - 用滑鼠點擊選取文字
   - 按住滑鼠左鍵拖拽選取文字
   - 雙擊選取單詞
   - 三擊選取段落
   - 按 Ctrl+A 全選
   - 按 Ctrl+C 複製
   - 按 Ctrl+X 剪下
   - 右鍵點擊查看選單
   - 滾輪滾動
3. 觀察這些操作是否被正確禁用

這個功能適用於需要防止用戶複製特定內容的場景。
"""
    
    text_widget.insert("1.0", test_text)
    
    # 綁定事件
    text_widget.bind('<Button-1>', on_text_click)
    text_widget.bind('<B1-Motion>', on_text_drag)
    text_widget.bind('<ButtonRelease-1>', on_text_release)
    text_widget.bind('<Button-3>', on_text_right_click)
    text_widget.bind('<Control-a>', on_text_select_all)
    text_widget.bind('<Control-c>', on_text_copy)
    text_widget.bind('<Control-x>', on_text_cut)
    text_widget.bind('<Double-Button-1>', on_text_double_click)
    text_widget.bind('<Triple-Button-1>', on_text_triple_click)
    text_widget.bind('<MouseWheel>', on_text_mousewheel)
    
    # 說明標籤
    info_label = ttk.Label(root, text="提示：這個測試模擬了主程式中的文字選取控制邏輯")
    info_label.pack(side=tk.BOTTOM, pady=5)
    
    root.mainloop()

if __name__ == "__main__":
    test_textbox_scroll_control() 