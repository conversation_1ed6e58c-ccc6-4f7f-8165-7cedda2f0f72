import tkinter as tk
from tkinter import filedialog
from PIL import Image, ImageTk
import pytesseract

# 可選：設定 tesseract 執行檔路徑
# pytesseract.pytesseract.tesseract_cmd = r"C:\Program Files\Tesseract-OCR\tesseract.exe"

# OCR：擷取框內區域辨識數字
def ocr_from_box(img, box):
    region = img.crop(box)
    text = pytesseract.image_to_string(region, config='--psm 6 digits')
    text = ''.join(filter(str.isdigit, text))
    return int(text) if text.isdigit() else None

# OCR + 計算邏輯
def extract_and_compute(img_path):
    img = Image.open(img_path)

    # 你提供的區域
    box1 = (154, 98, 154 + 100, 98 + 25)   # 字數
    box2 = (154, 124, 154 + 100, 124 + 25) # 字元數

    num1 = ocr_from_box(img, box1)
    num2 = ocr_from_box(img, box2)

    if num1 is not None and num2 is not None:
        result = round((num2 - num1) / 3 + num1)
        return num1, num2, result
    else:
        return num1, num2, None

# 顯示圖片 + 結果
def show_image_and_result():
    global last_result
    file_path = filedialog.askopenfilename()
    if not file_path:
        return

    img = Image.open(file_path)
    resized = img.resize((300, int(img.height * 300 / img.width)))
    tk_img = ImageTk.PhotoImage(resized)
    image_label.config(image=tk_img)
    image_label.image = tk_img

    n1, n2, result = extract_and_compute(file_path)
    if result is not None:
        last_result = result
        result_var.set(f"字數：{n1}, 字元數：{n2}\n結果（四捨五入）：{result}")
    else:
        result_var.set(f"辨識失敗：字數={n1}, 字元數={n2}")
        last_result = None

# 複製結果到剪貼簿
def copy_result():
    if last_result is not None:
        root.clipboard_clear()
        root.clipboard_append(str(last_result))
        result_var.set(result_var.get() + "\n✅ 結果已複製！")

# 建立 UI
root = tk.Tk()
root.title("固定區域OCR計算工具（含複製功能）")

image_label = tk.Label(root)
image_label.pack()

result_var = tk.StringVar()
result_label = tk.Label(root, textvariable=result_var, font=("Arial", 14), justify="left")
result_label.pack(pady=10)

load_button = tk.Button(root, text="載入圖片", command=show_image_and_result)
load_button.pack(pady=5)

copy_button = tk.Button(root, text="複製結果", command=copy_result)
copy_button.pack(pady=5)

last_result = None  # 存最後一次的結果
root.mainloop()
